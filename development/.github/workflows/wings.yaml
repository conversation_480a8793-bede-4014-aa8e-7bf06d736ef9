name: Wings

on:
  push:
    branches:
      - 'master'

jobs:
  build-and-push:
    name: Build and Push
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: docker/setup-qemu-action@v1
      - uses: docker/setup-buildx-action@v1
      - uses: docker/login-action@v1
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.REGISTRY_TOKEN }}

      - name: Build and Push image
        uses: docker/build-push-action@v2
        with:
          push: true
          platforms: linux/amd64,linux/arm64
          context: build/wings
          tags: ghcr.io/pterodactyl/development/wings:latest
