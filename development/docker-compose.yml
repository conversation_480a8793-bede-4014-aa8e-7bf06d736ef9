version: '3.8'

services:
  traefik:
    image: traefik:2.6
    restart: always
    networks:
      pterodactyl:
        aliases:
          - pterodactyl.test
          - minio.pterodactyl.test
          - s3.minio.pterodactyl.test
          - wings.pterodactyl.test
    ports:
      - "443:443"
      - "8080:8080"
    security_opt:
      - no-new-privileges=true
    volumes:
      - ./traefik:/etc/traefik:ro
      - ./docker/certificates:/etc/certs:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
  app:
    # image: ghcr.io/pterodactyl/development/panel:latest
    build:
      context: ./build/panel
      dockerfile: Dockerfile
    stop_grace_period: '1s'
    networks:
      pterodactyl:
        aliases:
          - pterodactyl.test
    volumes:
      - ./code/panel:/var/www/html
      - ./docker/certificates:/etc/certs:ro
      - ./docker/php:/etc/php/mods-available:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.app.tls=true"
      - "traefik.http.routers.app.rule=Host(`pterodactyl.test`)"
      - "traefik.http.routers.app.entrypoints=https"
      - "traefik.http.routers.app.service=app"
      - "traefik.http.services.app.loadbalancer.server.port=80"
      - "traefik.http.routers.webpack-dev-server.tls=true"
      - "traefik.http.routers.webpack-dev-server.rule=Host(`pterodactyl.test`) && PathPrefix(`/webpack@hmr`, `/sockjs-node`)"
      - "traefik.http.routers.webpack-dev-server.entrypoints=https"
      - "traefik.http.routers.webpack-dev-server.service=webpack-dev-server"
      - "traefik.http.services.webpack-dev-server.loadbalancer.server.port=8080"
    environment:
      TZ: ${TZ:-UTC}
      WWWUSER: ${WWWUSER:-$$(id -u)}
      WWWGROUP: ${WWWGROUP:-$$(id -g)}
      APP_URL: https://pterodactyl.test
      DB_HOST: mysql
      REDIS_HOST: redis
      DB_USERNAME: pterodactyl
      DB_PASSWORD: pterodactyl
      SESSION_DRIVER: redis
      CACHE_DRIVER: redis
  wings:
    image: ghcr.io/pterodactyl/development/wings:latest
    # build:
    #   context: ./build/wings
    #   dockerfile: Dockerfile
    stop_grace_period: '1s'
    tty: true
    stdin_open: true
    networks:
      - pterodactyl
    ports:
      - "2022:2022"
    volumes:
      - ./code/wings:/home/<USER>/wings
      - go_modules:/root/go
      - /tmp/pterodactyl:/tmp/pterodactyl
      - /var/lib/pterodactyl:/var/lib/pterodactyl
      - /var/run/docker.sock:/var/run/docker.sock:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.wings.tls=true"
      - "traefik.http.routers.wings.rule=Host(`wings.pterodactyl.test`)"
      - "traefik.http.routers.wings.entrypoints=https"
      - "traefik.http.services.wings.loadbalancer.server.port=8080"
    cap_add:
      - SYS_ADMIN
    devices:
      - /dev/loop0
      - /dev/loop-control
  mysql:
    image: mariadb:10.7
    restart: unless-stopped
    ports:
      - "3306:3306"
    command:
      - --innodb-buffer-pool-size=1G
      - --innodb-log-file-size=256M
      - --innodb-flush-log-at-trx-commit=0
      - --lower-case-table-names=1
    volumes:
      - mysql:/var/lib/mysql
      - ./docker/mysql:/docker-entrypoint-initdb.d:ro
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: panel
      MYSQL_USER: pterodactyl
      MYSQL_PASSWORD: pterodactyl
    networks:
      - pterodactyl
  redis:
    image: redis:alpine
    restart: unless-stopped
    networks:
      - pterodactyl
  minio:
    image: minio/minio:RELEASE.2022-02-05T04-40-59Z
    restart: unless-stopped
    command: server --console-address ":9001" /var/lib/minio
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: password
    expose:
      - 9000
      - 9001
    networks:
      pterodactyl:
        aliases:
          - s3.minio.pterodactyl.test
    volumes:
      - minio:/var/lib/minio
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio.tls=true"
      - "traefik.http.routers.minio.rule=Host(`s3.minio.pterodactyl.test`)"
      - "traefik.http.routers.minio.entrypoints=https"
      - "traefik.http.routers.minio.service=minio"
      - "traefik.http.routers.minio-console.tls=true"
      - "traefik.http.routers.minio-console.rule=Host(`minio.pterodactyl.test`)"
      - "traefik.http.routers.minio-console.entrypoints=https"
      - "traefik.http.routers.minio-console.service=minio-console"
      - "traefik.http.services.minio.loadbalancer.server.port=9000"
      - "traefik.http.services.minio-console.loadbalancer.server.port=9001"

networks:
  pterodactyl:
    driver: bridge

volumes:
  mysql:
    driver: local
  minio:
    driver: local
  go_modules:
    driver: local
