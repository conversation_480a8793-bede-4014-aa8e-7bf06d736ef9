#!/usr/bin/env bash
cd /var/www/html || exit 1

if [ ! -f ".env" ]; then
  cp .env.example .env
fi

sed -i "s/APP_ENV=.*/APP_ENV=local/" .env
sed -i "s/APP_DEBUG=.*/APP_DEBUG=true/" .env

composer install --no-interaction --prefer-dist --no-scripts --no-progress
php artisan config:clear
yarn install --no-progress

sudo chmod -R 775 storage/* bootstrap/cache

sudo service cron restart
# Forces supervisord to restart all of the processes with updated configurations.
sudo killall php-fpm8.1 php nginx
