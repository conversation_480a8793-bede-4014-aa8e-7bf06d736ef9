#!/usr/bin/env bash
set -x
set -e
set -o pipefail

if [ -n "$WWWUSER" ]; then
  usermod -u "$WWWUSER" pterodactyl
fi

if [ -d "/etc/php/mods-available" ]; then
  if [ -d "/etc/php/mods-available/cli" ]; then
    ln -sf /etc/php/mods-available/cli/* /etc/php/8.1/cli/conf.d
  fi
  if [ -d "/etc/php/mods-available/fpm" ]; then
    ln -sf /etc/php/mods-available/fpm/* /etc/php/8.1/fpm/conf.d
  fi
  find /etc/php/mods-available/. -maxdepth 1 -type f -print0 | while read -d $'\0' file
  do
    ln -sf "$file" /etc/php/8.1/cli/conf.d/
    ln -sf "$file" /etc/php/8.1/fpm/conf.d/
  done
fi

if [ -d "/etc/certs" ]; then
  mkdir -p /usr/local/share/ca-certificates/mkcert
  cp /etc/certs/pterodactyl*.pem /usr/local/share/ca-certificates/mkcert/
  update-ca-certificates
fi

cd /var/www/html || exit 1
chown -R pterodactyl:www-data storage
chmod -R 777 storage/* bootstrap/cache

if [ $# -gt 0 ]; then
  sudo su -l pterodactyl -c "/bin/bash"
else
  service cron start
  /usr/bin/supervisord -c /etc/supervisord.conf -n
fi
